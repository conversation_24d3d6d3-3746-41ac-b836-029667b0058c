"use client";

import React, {
	createContext,
	useContext,
	useEffect,
	useState,
	ReactNode,
} from "react";
import { useRouter, usePathname } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import {
	onAuthStateChanged,
	User as FirebaseUser,
	signOut,
} from "firebase/auth";
import { auth } from "@/lib/firebase";
import { RootState } from "@/store/store";
import {
	setCredentials,
	clearCredentials,
	setLoading,
	setError,
} from "@/store/slices/authSlice";
import { useVerifyTokenMutation } from "@/store/api/authApi";
import { CookieManager } from "@/utils/cookieManager";
import { ROUTES, isPublicRoute, getRedirectAfterLogin } from "@/config/routes";
import { ApiError } from "@/types";

interface AuthContextType {
	isInitialized: boolean;
	isLoading: boolean;
	error: string | null;
	clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
	children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
	const dispatch = useDispatch();
	const router = useRouter();
	const pathname = usePathname();

	const { user, isAuthenticated, isLoading, error } = useSelector(
		(state: RootState) => state.auth
	);

	const [isInitialized, setIsInitialized] = useState(false);
	const [verifyToken] = useVerifyTokenMutation();

	// Clear error function
	const clearError = () => {
		dispatch(setError(null));
	};

	// Handle Firebase auth state changes
	useEffect(() => {
		if (typeof window === "undefined") return;

		const unsubscribe = onAuthStateChanged(
			auth,
			async (firebaseUser: FirebaseUser | null) => {
				try {
					dispatch(setLoading(true));

					if (firebaseUser) {
						// User is signed in
						const idToken = await firebaseUser.getIdToken();

						// Set auth token cookie
						CookieManager.setAuthToken(idToken);

						try {
							// Verify token with backend
							const response = await verifyToken({ idToken }).unwrap();

							dispatch(
								setCredentials({
									user: response.user,
									token: response.token,
								})
							);

							// Handle redirects based on current route and profile status
							handleAuthRedirect(response.user, pathname);
						} catch (error: unknown) {
							const apiError = error as ApiError;

							if (apiError.status === 404) {
								// User not found in backend, redirect to role selection
								router.push(ROUTES.SELECT_ROLE);
							} else {
								// Other errors, clear auth and redirect to login if on protected route
								dispatch(clearCredentials());
								CookieManager.removeAuthToken();

								if (!isPublicRoute(pathname)) {
									router.push(ROUTES.LOGIN);
								}
							}
						}
					} else {
						// User is signed out
						dispatch(clearCredentials());
						CookieManager.removeAuthToken();

						// Redirect to login if on protected route
						if (!isPublicRoute(pathname)) {
							router.push(ROUTES.LOGIN);
						}
					}
				} catch (error) {
					console.error("Auth state change error:", error);
					dispatch(setError("Authentication error occurred"));
				} finally {
					dispatch(setLoading(false));
					setIsInitialized(true);
				}
			}
		);

		return () => unsubscribe();
	}, [dispatch, router, pathname, verifyToken]);

	// Handle authentication-based redirects
	const handleAuthRedirect = (user: any, currentPath: string) => {
		// Don't redirect if already on the correct page
		if (currentPath === ROUTES.LOGIN && user) {
			const redirectPath = getRedirectAfterLogin(
				user.role,
				user.profileCompleted
			);
			router.push(redirectPath);
			return;
		}

		// If profile is incomplete and not on complete-profile page
		if (!user.profileCompleted && currentPath !== ROUTES.COMPLETE_PROFILE) {
			router.push(ROUTES.COMPLETE_PROFILE);
			return;
		}

		// If profile is complete and on complete-profile page
		if (user.profileCompleted && currentPath === ROUTES.COMPLETE_PROFILE) {
			router.push(ROUTES.DASHBOARD.HOME);
			return;
		}
	};

	const contextValue: AuthContextType = {
		isInitialized,
		isLoading,
		error,
		clearError,
	};

	return (
		<AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
	);
}

export function useAuthContext(): AuthContextType {
	const context = useContext(AuthContext);
	if (context === undefined) {
		throw new Error("useAuthContext must be used within an AuthProvider");
	}
	return context;
}
