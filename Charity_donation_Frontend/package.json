{"name": "charity-donation-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/x-date-pickers": "^8.3.1", "@radix-ui/react-slot": "^1.2.3", "@reduxjs/toolkit": "^2.8.1", "@stripe/stripe-js": "^4.10.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "axios": "^1.9.0", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "firebase": "^11.7.1", "formik": "^2.4.6", "framer-motion": "^12.10.5", "lucide-react": "^0.510.0", "next": "^15.3.1", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.0", "yup": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.6", "@types/react": "^19", "@types/react-redux": "^7.1.34", "tailwindcss": "^4.1.6", "typescript": "^5"}}